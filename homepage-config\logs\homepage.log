[2025-06-14T20:05:50.361Z] info: kubernetes.yaml was copied to the config folder
[2025-06-14T20:08:36.657Z] info: docker.yaml was copied to the config folder
[2025-06-14T20:08:36.706Z] info: bookmarks.yaml was copied to the config folder
[2025-06-14T20:08:36.886Z] info: widgets.yaml was copied to the config folder
[2025-06-14T20:08:37.001Z] info: custom.css was copied to the config folder
[2025-06-14T20:08:37.013Z] info: custom.js was copied to the config folder
[2025-06-14T21:25:57.515Z] error: Failed to load services.yaml, please check for errors
[2025-06-14T21:25:57.516Z] error: TypeError: t.map is not a function
[2025-06-14T21:25:57.605Z] error: Failed to load services.yaml, please check for errors
[2025-06-14T21:25:57.605Z] error: TypeError: t.map is not a function
[2025-06-14T21:25:57.949Z] error: Failed to load services.yaml, please check for errors
[2025-06-14T21:25:57.949Z] error: TypeError: t.map is not a function
[2025-06-14T21:26:16.819Z] error: Failed to load services.yaml, please check for errors
[2025-06-14T21:26:16.819Z] error: TypeError: t.map is not a function
[2025-06-14T21:59:44.610Z] error: Failed to load services.yaml, please check for errors
[2025-06-14T21:59:44.611Z] error: TypeError: t.map is not a function
[2025-06-14T21:59:47.764Z] error: Failed to load services.yaml, please check for errors
[2025-06-14T21:59:47.764Z] error: TypeError: t.map is not a function
[2025-06-14T22:13:08.619Z] info: services.yaml was copied to the config folder
[2025-06-19T13:49:19.637Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:19.891Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:19.893Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:19.896Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:19.900Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:19.912Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:19.914Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:19.916Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:19.918Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:19.925Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:19.940Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:23.492Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.761Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.763Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.789Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.867Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.888Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.891Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.900Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.901Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.902Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.905Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.906Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.920Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.923Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.925Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:38.973Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.122Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.193Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.205Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.206Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.208Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.210Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.214Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.215Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.228Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.230Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.232Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.234Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:49:54.255Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.321Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.328Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.348Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.419Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.424Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.427Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.429Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.431Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.433Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.445Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.447Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.449Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.455Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.457Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T13:50:21.493Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:24.053Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:24.064Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:24.070Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:24.073Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:24.076Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:24.079Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:24.110Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:24.113Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:24.116Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:24.119Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:24.122Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:24.179Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.361Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.704Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.709Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.711Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.715Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.718Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.720Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.748Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.752Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.753Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.755Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.757Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:32.783Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:36.927Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:36.959Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:37.069Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:37.074Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:37.092Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:37.094Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:37.096Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:37.097Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:37.100Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:37.102Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:37.126Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:37.128Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:37.130Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:37.161Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:57.841Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:57.843Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:57.871Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:57.943Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:57.945Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:57.948Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:57.975Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:57.977Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:57.978Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:57.980Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:57.982Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:57.983Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:58.012Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:58.015Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:02:58.033Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.569Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.585Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.633Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.648Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.651Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.652Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.654Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.655Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.657Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.680Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.682Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.684Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.687Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:01.714Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.016Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.049Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.104Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.107Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.109Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.112Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.114Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.115Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.129Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.131Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.132Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.134Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.136Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:03:04.152Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.111Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.115Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.143Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.226Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.229Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.231Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.233Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.235Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.238Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.245Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.247Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.249Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.257Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.260Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:04:28.280Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.838Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.843Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.870Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.932Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.939Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.941Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.943Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.945Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.946Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.948Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.956Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.960Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.969Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.970Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:06:59.987Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.786Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.817Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.873Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.875Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.878Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.880Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.881Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.883Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.892Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.894Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.902Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.903Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.904Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:07:02.923Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.478Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.488Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.526Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.595Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.600Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.605Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.609Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.613Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.618Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.622Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.625Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.627Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.630Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.639Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:25.645Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.367Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.405Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.451Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.454Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.459Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.462Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.465Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.466Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.480Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.489Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.491Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.493Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.495Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:28.520Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:53.473Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:54.178Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:54.181Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:54.183Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:54.188Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:54.191Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:54.216Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:54.232Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:54.234Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:54.238Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:54.241Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:12:54.304Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:18.639Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:18.910Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:18.913Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:18.915Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:18.919Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:18.921Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:18.923Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:18.947Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:18.950Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:18.952Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:18.955Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:18.960Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:27.501Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:27.503Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:28.860Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:29.003Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:29.005Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:29.012Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:29.015Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:29.017Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:29.019Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:29.021Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:29.025Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:29.031Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:29.035Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:29.069Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:13:29.102Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:23.135Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:23.354Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:23.357Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:23.359Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:23.361Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:23.362Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:23.365Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:23.373Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:23.374Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:23.376Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:23.379Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:23.442Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:32.855Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:33.273Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:33.276Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:33.278Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:33.281Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:33.282Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:33.286Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:33.292Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:33.296Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:33.316Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:33.319Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:33.382Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:14:33.504Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:20:23.984Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:20:23.988Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:20:25.077Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:50.009Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:50.185Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:50.188Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:50.200Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:50.202Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:50.206Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:50.209Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:50.212Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:50.215Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:50.220Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:50.259Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:50.321Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:56.343Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:56.348Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:39:57.731Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:00.369Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.608Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.713Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.717Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.720Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.722Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.723Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.726Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.728Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.737Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.739Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.740Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.806Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:01.831Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:03.887Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.069Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.308Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.310Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.325Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.327Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.329Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.331Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.332Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.345Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.346Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.349Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.371Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:40:05.407Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.210Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.237Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.316Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.320Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.324Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.330Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.335Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.337Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.340Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.343Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.346Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.350Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:02.372Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:04.314Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:04.709Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:43:04.711Z] error: Host validation failed for: hypatia.tailcdc8f2.ts.net:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.292Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.405Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.412Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.416Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.419Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.423Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.426Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.428Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.430Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.437Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.444Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.459Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-06-19T14:47:25.503Z] error: Host validation failed for: **************:3000. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
